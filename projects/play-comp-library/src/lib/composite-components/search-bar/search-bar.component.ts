import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { IconComponent } from '../../components/icon/icon.component';

@Component({
  selector: 'ava-search-bar',
  imports: [CommonModule, AvaTextboxComponent, IconComponent],
  templateUrl: './search-bar.component.html',
  styleUrl: './search-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchBarComponent extends AvaTextboxComponent {
  @Output() searchClick = new EventEmitter<string>();
  @Input() searchIconColor = '#000000';
  @Input() sendIconColor = '#000000';
  searchValue: string = '';
  get iconSize(): number {
    return (
      ({ xl: 24, lg: 24, md: 20, xs: 16 } as Record<string, number>)[
        this.size
      ] ?? 16
    );
  }

  get iconSearchColor(): string {
    if (this.disabled) return 'var(--button-icon-color-disabled)';
    return this.searchIconColor;
  }

  get iconSendColor(): string {
    if (this.disabled) return 'var(--button-icon-color-disabled)';
    return this.sendIconColor;
  }

  search() {
    this.searchClick.emit(this.searchValue);
  }
  searchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchValue = target.value;
  }
}

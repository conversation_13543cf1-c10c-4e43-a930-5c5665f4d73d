/* --- Configuration Variables --- */
/* These can be overridden by parent components for custom themes. */
:host {
  --sidebar-bg-color: var(--color-background-primary);
  --sidebar-text-color: var(--color-text-primary);
  --sidebar-border-color: var(--color-border-default);
  --sidebar-highlight-color: var(--color-surface-interactive-default);
  --sidebar-highlight-text-color: var(--color-text-on-brand);
  --sidebar-hover-bg-color: var(--color-surface-subtle-hover);
  --sidebar-z-index: 1000;
  --sidebar-transition-duration: var(--global-motion-duration-standard);

  /* Size variants using global spacing tokens */
  --sidebar-width-small: 220px;
  --sidebar-width-medium: 260px;
  --sidebar-width-large: 300px;
  --sidebar-collapsed-width: 80px;

  /* Private variable that resolves to the correct width based on size input */
  --_sidebar-width: var(--sidebar-width-medium);
}

/* --- Host Element Styles --- */
:host {
  display: block;
  position: relative;
  background-color: var(--sidebar-bg-color);
  color: var(--sidebar-text-color);
  transition: width var(--sidebar-transition-duration) ease-in-out,
              min-width var(--sidebar-transition-duration) ease-in-out;
  flex-shrink: 0; /* Prevents the sidebar from shrinking in a flex container */
  width: var(--_sidebar-width);
}

/* --- Size Modifiers --- */
:host(.sidebar--small) { --_sidebar-width: var(--sidebar-width-small); }
:host(.sidebar--medium) { --_sidebar-width: var(--sidebar-width-medium); }
:host(.sidebar--large) { --_sidebar-width: var(--sidebar-width-large); }

/* --- Position Modifiers --- */
:host(.sidebar--left) {
  border-right: 1px solid var(--sidebar-border-color);
}

:host(.sidebar--right) {
  order: 1; /* Places the sidebar to the right in a flex container */
  border-left: 1px solid var(--sidebar-border-color);
  .sidebar__toggle {
    left: -15px;
    right: auto;
    transform: translateY(-50%) rotate(180deg);
  }
}

/* --- Collapsed State --- */
:host(.sidebar--collapsed) {
  width: var(--sidebar-collapsed-width);

  .sidebar__toggle {
    transform: translateY(-50%) rotate(180deg);
  }

  // Adjust toggle arrow for right-positioned sidebar when collapsed
  &.sidebar--right .sidebar__toggle {
    transform: translateY(-50%) rotate(0deg);
  }

  // Hide text elements within projected content when collapsed.
  // The consumer must add the `.sidebar-item-text` class to the elements they want to hide.
  ::ng-deep .sidebar-item-text {
    display: none;
    opacity: 0;
    visibility: hidden;
  }
}

/* --- Inner Structure & Toggle --- */
.sidebar__inner {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar__content {
  flex-grow: 1; /* Allows content to fill available space */
}

.sidebar__header, .sidebar__footer {
  flex-shrink: 0;
}

.sidebar__toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  transform: translateY(-50%);
  z-index: calc(var(--sidebar-z-index) + 1);
  width: 30px;
  height: 30px;
  border-radius: var(--global-radius-circle);
  background-color: var(--sidebar-highlight-color);
  color: var(--sidebar-highlight-text-color);
  border: 2px solid var(--sidebar-bg-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--sidebar-transition-duration) ease;
  box-shadow: var(--global-elevation-02);

  svg {
    fill: currentColor;
  }
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
  :host {
    /* On mobile, make sidebar full width when expanded */
    --sidebar-width-small: 100vw;
    --sidebar-width-medium: 100vw;
    --sidebar-width-large: 100vw;
    --sidebar-collapsed-width: 60px;

    /* Position fixed on mobile for overlay behavior */
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 9999;
  }

  :host(.sidebar--collapsed) {
    width: var(--sidebar-collapsed-width);
  }

  .sidebar__toggle {
    /* Make toggle button more accessible on mobile */
    width: 40px;
    height: 40px;
    right: -20px;
  }
}

@media (max-width: 480px) {
  :host {
    --sidebar-collapsed-width: 50px;
  }

  .sidebar__toggle {
    width: 35px;
    height: 35px;
    right: -17px;
  }
}

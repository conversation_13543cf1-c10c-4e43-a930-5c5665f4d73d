import { Component, Input, Output, EventEmitter, signal, HostBinding } from '@angular/core';
import { CommonModule } from '@angular/common';

// Define types for better code clarity and safety
export type SidebarPosition = 'left' | 'right';
export type SidebarSize = 'small' | 'medium' | 'large';

@Component({
  selector: 'ava-sidebar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  /**
   * Defines the sidebar's position on the screen.
   * @type {SidebarPosition}
   * @default 'left'
   */
  @Input() position: SidebarPosition = 'left';

  /**
   * Sets the width of the expanded sidebar.
   * @type {SidebarSize}
   * @default 'medium'
   */
  @Input() size: SidebarSize = 'medium';

  /**
   * Determines if the sidebar can be collapsed.
   * @type {boolean}
   * @default true
   */
  @Input() collapsible: boolean = true;

  // Use a signal for reactive state management of the collapsed state.
  private _collapsed = signal<boolean>(false);

  /**
   * An input to programmatically control the collapsed state from a parent component.
   */
  @Input()
  set collapsed(value: boolean) {
    this._collapsed.set(value);
  }
  get collapsed(): boolean {
    return this._collapsed();
  }

  /**
   * Emits the new collapsed state whenever it changes.
   * This enables two-way binding `[(collapsed)]`.
   */
  @Output() collapsedChange = new EventEmitter<boolean>();

  /**
   * Binds CSS classes to the host element based on the component's state.
   * This is an efficient way to apply dynamic styling.
   */
  @HostBinding('class')
  get hostClasses(): string {
    return `
      sidebar
      sidebar--${this.position}
      sidebar--${this.size}
      ${this.collapsed ? 'sidebar--collapsed' : ''}
    `;
  }

  /**
   * Toggles the collapsed state and emits the change.
   */
  toggleCollapse(): void {
    if (this.collapsible) {
      this._collapsed.update(value => !value);
      this.collapsedChange.emit(this._collapsed());
    }
  }
}
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TreeviewComponent, TreeNode } from './treeview.component';

const treeData: TreeNode[] = [
  {
    id: '1',
    name: 'Engineering',
    icon: 'folder',
    expanded: false,
    selected: false,
    children: [
      { id: '1.1', name: 'Frontend', icon: 'folder', selected: false },
      { id: '1.2', name: 'Backend', icon: 'folder', selected: false },
    ],
  },
  {
    id: '2',
    name: 'Mobile',
    icon: 'folder',
    expanded: false,
    selected: false,
    children: [
      { id: '2.1', name: 'UI', icon: 'folder', selected: false },
      { id: '2.2', name: 'Sap', icon: 'folder', selected: false },
    ],
  },
  { id: '3', name: 'Marketing', icon: 'folder', selected: false },
  { id: '4', name: 'Operations', icon: 'folder', selected: false },
];

describe('TreeviewComponent with treeData', () => {
  let component: TreeviewComponent;
  let fixture: ComponentFixture<TreeviewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TreeviewComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TreeviewComponent);
    component = fixture.componentInstance;
    component.nodes = [...treeData];
    fixture.detectChanges();
  });

  it('should create with treeData', () => {
    expect(component).toBeTruthy();
    expect(component.nodes.length).toBe(4);
  });

  it('should expand and collapse Engineering node', () => {
    const engineering = component.nodes[0];
    expect(engineering.expanded).toBeFalse();

    component.toggleExpand(engineering);
    expect(engineering.expanded).toBeTrue();

    component.toggleExpand(engineering);
    expect(engineering.expanded).toBeFalse();
  });

  it('should emit selected node event for Marketing', () => {
    spyOn(component.nodeSelect, 'emit');
    const marketing = component.nodes[2];

    component.selectNode(marketing);

    expect(component.nodeSelect.emit).toHaveBeenCalledWith(marketing);
  });

  it('should calculate indent properly for nested child', () => {
    component.size = 'md';
    const indent = component.calculateIndent(2);
    expect(indent).toBe(50);
  });

  it('should not expand Marketing because it has no children', () => {
    const marketing = component.nodes[2];
    component.toggleExpand(marketing);
    expect(marketing.expanded).toBeUndefined();
  });

  it('should expand Mobile and access its children', () => {
    const mobile = component.nodes[1];
    component.toggleExpand(mobile);
    expect(mobile.expanded).toBeTrue();
    expect(mobile.children?.length).toBe(2);
    expect(mobile.children?.[0].name).toBe('UI');
  });

  it('should expand multiple parent nodes independently', () => {
    const engineering = component.nodes[0];
    const mobile = component.nodes[1];

    component.toggleExpand(engineering);
    component.toggleExpand(mobile);

    expect(engineering.expanded).toBeTrue();
    expect(mobile.expanded).toBeTrue();
  });

  it('should emit event when a nested child node is selected', () => {
    spyOn(component.nodeSelect, 'emit');
    const frontend = component.nodes[0].children?.[0]!;

    component.selectNode(frontend);

    expect(component.nodeSelect.emit).toHaveBeenCalledWith(frontend);
  });

  it('should calculate indent for leaf nodes', () => {
    component.size = 'sm';
    const indent = component.calculateIndent(1);
    expect(indent).toBe(25);
  });

  it('should toggle expanded state correctly on repeated calls', () => {
    const engineering = component.nodes[0];

    component.toggleExpand(engineering);
    expect(engineering.expanded).toBeTrue();

    component.toggleExpand(engineering);
    expect(engineering.expanded).toBeFalse();

    component.toggleExpand(engineering);
    expect(engineering.expanded).toBeTrue();
  });

  it('should create a new array reference when toggling a node', () => {
    const prevNodes = component.nodes;
    component.toggleExpand(component.nodes[0]);
    expect(component.nodes).not.toBe(prevNodes);
  });

  it('should emit events for multiple sequential node selections', () => {
    spyOn(component.nodeSelect, 'emit');
    const engineering = component.nodes[0];
    const operations = component.nodes[3];

    component.selectNode(engineering);
    component.selectNode(operations);

    expect(component.nodeSelect.emit).toHaveBeenCalledTimes(2);
    expect(component.nodeSelect.emit).toHaveBeenCalledWith(engineering);
    expect(component.nodeSelect.emit).toHaveBeenCalledWith(operations);
  });
});

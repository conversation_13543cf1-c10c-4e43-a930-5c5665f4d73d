<div class="cards-container">
    <!-- First Card - Simple Card -->
    <div class="card-wrapper">
        <ava-default-card class="simple-card">
            <div class="card-content">
                <h2 class="card-heading">Heading lable</h2>
                <p class="card-description">Description text goes here ax 2 lines after that truncation will turned on
                </p>
                <div class="card-footer">
                    <span class="footer-text">michaelscottascendion.com</span>
                </div>
            </div>
        </ava-default-card>
    </div>

    <!-- Second Card - Complex Card -->
    <div class="card-wrapper">
        <ava-default-card class="complex-card">
            <div class="card-content">
                <!-- Header Section -->
                <div class="card-header">
                    <div class="header-left">
                        <div class="icon-container">
                            <ava-icon iconName="grid" [iconSize]="24" iconColor="#000000"></ava-icon>
                        </div>
                        <h2 class="card-title">Ruby to Springboot</h2>
                    </div>
                    <div class="header-right">
                        <ava-icon iconName="star" [iconSize]="16" iconColor="#D9D9D9"></ava-icon>
                        <span class="rating-text">5.0</span>
                        <ava-tag label="Tag" color="error" size="md" [pill]="false"></ava-tag>
                        <ava-icon iconName="more-vertical" [iconSize]="20" iconColor="#000000"></ava-icon>
                    </div>
                </div>

                <!-- Description -->
                <p class="card-description">Effortlessly convert Ruby code to Spring Boot with optimised migration</p>

                <!-- Tags Section -->
                <div class="tags-section">
                    <ava-tag label="Ascendion" color="default" size="lg" [pill]="false"></ava-tag>
                    <ava-tag label="Individual buttons" color="default" size="lg" [pill]="false"></ava-tag>
                    <ava-tag label="Agent" color="default" size="lg" [pill]="false"></ava-tag>
                    <ava-tag label="Label" color="default" size="lg" [pill]="false"></ava-tag>
                    <ava-tag label="Label" color="default" size="lg" [pill]="false"></ava-tag>
                </div>

                <!-- Meta Info Section -->
                <div class="meta-info-section">
                    <div class="meta-left">
                        <ava-icon iconName="user" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <span class="meta-text">michaelscottascendion.com</span>
                    </div>
                    <div class="meta-right">
                        <ava-icon iconName="calendar-days" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <span class="meta-text">12 May 2025</span>
                    </div>
                </div>

                <!-- Action Section -->
                <div class="action-section">
                    <div class="action-left">
                        <ava-icon iconName="user" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <span class="action-number">24</span>
                    </div>
                    <div class="action-right">
                        <ava-icon iconName="trash-2" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <ava-icon iconName="copy" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <ava-icon iconName="code" [iconSize]="16" iconColor="#898E99"></ava-icon>
                        <ava-button iconName="play" iconPosition="only" variant="primary" size="xsmall" [pill]="false" width="45px" height="36px">
                        </ava-button>
                    </div>
                </div>
            </div>
        </ava-default-card>
    </div>
</div>
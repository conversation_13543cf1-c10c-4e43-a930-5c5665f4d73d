import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'test-sidebar',
  standalone: true,
  imports: [CommonModule, SidebarComponent, ButtonComponent, IconComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrl: './test-sidebar.component.scss',
})
export class TestSidebarComponent {
  isSidebarCollapsed = false;

  // State for different sidebar variants
  smallSidebarCollapsed = false;
  mediumSidebarCollapsed = false;
  largeSidebarCollapsed = false;
  leftSidebarCollapsed = false;
  rightSidebarCollapsed = false;

  // Current demo variant
  currentSize: 'small' | 'medium' | 'large' = 'medium';
  currentPosition: 'left' | 'right' = 'left';

  // Switch between variants
  switchVariant(variant: 'small' | 'medium' | 'large') {
    this.currentSize = variant;
  }

  switchPosition(position: 'left' | 'right') {
    this.currentPosition = position;
  }
}

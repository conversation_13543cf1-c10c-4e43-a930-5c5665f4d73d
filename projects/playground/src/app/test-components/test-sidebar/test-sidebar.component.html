<div class="app-layout">
  <ava-sidebar [(collapsed)]="isSidebarCollapsed" [size]="currentSize" [position]="currentPosition">

    <div sidebar-header class="sidebar-section-header">
        <img src="../../../../../playground/src/assets/ascendion_logo_wide.svg" alt="Ascendion" class="logo">
      <img src="../../../../../playground/src/assets/ascendion_logo.svg" alt="A" class="logo--collapsed">
    </div>

    <div sidebar-content>
      <div class="search-bar">
        <ava-icon iconName="search" [iconSize]="20" iconColor="#666"></ava-icon>
        <input type="text" placeholder="Search" class="sidebar-item-text">
      </div>
      <nav class="main-nav">
        <div class="nav-items">
          <div class="nav-item active">
            <ava-icon iconName="layout-dashboard" [iconSize]="20" iconColor="white"></ava-icon>
            <span class="sidebar-item-text">Dashboard</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="trending-up" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Requirement Analyser</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="shield-check" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Test Case Scenario</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="plus-circle" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Test Case Creation</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="file-text" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Automation Script Creation</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="repeat" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Conversion</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="zap" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Test Optimization</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="database" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Data Management</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="clock" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Request History</span>
          </div>
          <div class="nav-item">
            <ava-icon iconName="message-circle" [iconSize]="20" iconColor="#666"></ava-icon>
            <span class="sidebar-item-text">Ask Me</span>
          </div>
        </div>
      </nav>
    </div>

    <div sidebar-footer class="sidebar-section-footer">
      <div class="profile">
        <img src="https://i.imgur.com/vHtwc25.png" alt="John Doe">
        <div class="profile-info sidebar-item-text">
          <strong>John Doe</strong>
          <span>View Profile</span>
        </div>
      </div>
    </div>

  </ava-sidebar>

  <main class="main-content">
    <div class="content-header">
      <h1>Sidebar Component Demo</h1>
      <p>Interactive demonstration of the sidebar component with all variants and features.</p>
    </div>

    <div class="demo-controls">
      <div class="control-group">
        <h3>Size Variants</h3>
        <div class="button-group">
          <button
            [class.active]="currentSize === 'small'"
            (click)="switchVariant('small')"
            class="demo-button">
            Small
          </button>
          <button
            [class.active]="currentSize === 'medium'"
            (click)="switchVariant('medium')"
            class="demo-button">
            Medium
          </button>
          <button
            [class.active]="currentSize === 'large'"
            (click)="switchVariant('large')"
            class="demo-button">
            Large
          </button>
        </div>
      </div>

      <div class="control-group">
        <h3>Position Variants</h3>
        <div class="button-group">
          <button
            [class.active]="currentPosition === 'left'"
            (click)="switchPosition('left')"
            class="demo-button">
            Left
          </button>
          <button
            [class.active]="currentPosition === 'right'"
            (click)="switchPosition('right')"
            class="demo-button">
            Right
          </button>
        </div>
      </div>

      <div class="control-group">
        <h3>Collapse Control</h3>
        <button
          (click)="isSidebarCollapsed = !isSidebarCollapsed"
          class="demo-button toggle-button">
          {{ isSidebarCollapsed ? 'Expand' : 'Collapse' }} Sidebar
        </button>
      </div>
    </div>

    <div class="demo-info">
      <h3>Current Configuration</h3>
      <ul>
        <li><strong>Size:</strong> {{ currentSize }}</li>
        <li><strong>Position:</strong> {{ currentPosition }}</li>
        <li><strong>Collapsed:</strong> {{ isSidebarCollapsed ? 'Yes' : 'No' }}</li>
      </ul>
    </div>
  </main>
</div>

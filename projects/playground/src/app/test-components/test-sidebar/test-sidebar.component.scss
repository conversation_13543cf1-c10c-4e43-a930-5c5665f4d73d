/* --- Global Layout --- */
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex-grow: 1;
  padding: var(--global-spacing-8);
  background-color: var(--color-background-subtle);
  overflow-y: auto;

  .content-header {
    margin-bottom: var(--global-spacing-8);

    h1 {
      color: var(--color-text-primary);
      font-family: var(--font-family-heading);
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--global-spacing-2);
    }

    p {
      color: var(--color-text-secondary);
      font-family: var(--font-family-body);
      font-size: var(--global-font-size-lg);
    }
  }

  .demo-controls {
    background: var(--color-background-primary);
    border-radius: var(--global-radius-lg);
    padding: var(--global-spacing-6);
    margin-bottom: var(--global-spacing-6);
    box-shadow: var(--global-elevation-01);

    .control-group {
      margin-bottom: var(--global-spacing-6);

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        color: var(--color-text-primary);
        font-family: var(--font-family-heading);
        font-weight: var(--font-weight-semibold);
        margin-bottom: var(--global-spacing-3);
        font-size: var(--global-font-size-md);
      }

      .button-group {
        display: flex;
        gap: var(--global-spacing-2);
        flex-wrap: wrap;
      }

      .demo-button {
        padding: var(--global-spacing-2) var(--global-spacing-4);
        border: 1px solid var(--color-border-default);
        border-radius: var(--global-radius-md);
        background: var(--color-background-primary);
        color: var(--color-text-primary);
        font-family: var(--font-family-body);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: all var(--global-motion-duration-fast) ease;

        &:hover {
          background: var(--color-surface-subtle-hover);
          border-color: var(--color-border-interactive);
        }

        &.active {
          background: var(--color-surface-interactive-default);
          color: var(--color-text-on-brand);
          border-color: var(--color-surface-interactive-default);
        }

        &.toggle-button {
          background: linear-gradient(135deg, #e91e63 0%, #f8506b 100%);
          color: white;
          border-color: transparent;

          &:hover {
            background: linear-gradient(135deg, #d81b60 0%, #f73f5b 100%);
          }
        }
      }
    }
  }

  .demo-info {
    background: var(--color-background-primary);
    border-radius: var(--global-radius-lg);
    padding: var(--global-spacing-6);
    box-shadow: var(--global-elevation-01);

    h3 {
      color: var(--color-text-primary);
      font-family: var(--font-family-heading);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--global-spacing-4);
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: var(--global-spacing-2) 0;
        color: var(--color-text-secondary);
        font-family: var(--font-family-body);
        border-bottom: 1px solid var(--color-border-subtle);

        &:last-child {
          border-bottom: none;
        }

        strong {
          color: var(--color-text-primary);
          font-weight: var(--font-weight-semibold);
        }
      }
    }
  }
}

/* --- Projected Content Styling --- */
/* These styles control the look of the content inside the sidebar */

.sidebar-section-header {
  padding: var(--global-spacing-6) var(--global-spacing-4);
  .logo {
    max-height: 40px;
    display: block;
  }
  .logo--collapsed {
    display: none;
    max-height: 35px;
    margin: 0 auto;
  }
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-background-primary);
  border-radius: var(--global-radius-md);
  padding: var(--global-spacing-3) var(--global-spacing-3);
  margin: 0 var(--global-spacing-4) var(--global-spacing-4);
  border: 1px solid var(--color-border-default);
  input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    margin-left: var(--global-spacing-3);
    color: var(--color-text-primary);
    font-family: var(--font-family-body);
  }
}

.main-nav .nav-items {
  padding: 0;
  margin: 0;
}
.nav-item {
  display: flex;
  align-items: center;
  padding: var(--global-spacing-3) var(--global-spacing-6);
  margin: var(--global-spacing-1) var(--global-spacing-4);
  cursor: pointer;
  border-radius: var(--global-radius-md);
  transition: background-color var(--global-motion-duration-fast) ease,
              color var(--global-motion-duration-fast) ease;
  font-family: var(--font-family-body);
  font-weight: var(--font-weight-medium);
  ava-icon {
    width: 24px;
    margin-right: var(--global-spacing-4);
    flex-shrink: 0;
  }
  &:hover {
    background-color: var(--color-surface-subtle-hover);
  }
  &.active {
    background: linear-gradient(135deg, #e91e63 0%, #f8506b 100%);
    color: white;
    border-radius: var(--global-radius-lg);
    box-shadow: var(--global-elevation-02);

    ava-icon {
      color: white !important;
    }

    span {
      color: white;
      font-weight: var(--font-weight-semibold);
    }
  }
}

.sidebar-section-footer {
  border-top: 1px solid var(--color-border-default);
  margin-top: var(--global-spacing-4);
  padding: var(--global-spacing-6) var(--global-spacing-4);
}

.profile {
  display: flex;
  align-items: center;
  img {
    width: 40px;
    height: 40px;
    border-radius: var(--global-radius-circle);
    margin-right: var(--global-spacing-4);
  }
  .profile-info {
    line-height: var(--global-line-height-tight);
    font-family: var(--font-family-body);
    strong {
      display: block;
      color: var(--color-text-primary);
      font-weight: var(--font-weight-semibold);
    }
    span {
      font-size: var(--global-font-size-sm);
      color: var(--color-text-secondary);
    }
  }
}

/* --- Styles for when the sidebar is collapsed --- */
/* We use the parent selector to change projected content styles based on the sidebar's state */
:host-context(.sidebar--collapsed) {
  .sidebar-section-header {
    .logo { display: none; }
    .logo--collapsed { display: block; }
  }
  .search-bar { justify-content: center; }
  .nav-item {
    justify-content: center;
    ava-icon { margin-right: 0; }
  }
  .profile { justify-content: center; img { margin-right: 0; } }
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
  .app-layout {
    flex-direction: column;
  }

  .main-content {
    padding: var(--global-spacing-4);
    margin-left: 0;
  }

  /* Adjust spacing for mobile */
  .sidebar-section-header {
    padding: var(--global-spacing-4) var(--global-spacing-3);
  }

  .search-bar {
    margin: 0 var(--global-spacing-3) var(--global-spacing-3);
    padding: var(--global-spacing-2) var(--global-spacing-3);
  }

  .nav-item {
    padding: var(--global-spacing-3) var(--global-spacing-4);
    margin: var(--global-spacing-1) var(--global-spacing-3);
  }

  .sidebar-section-footer {
    padding: var(--global-spacing-4) var(--global-spacing-3);
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--global-spacing-3);
  }

  .nav-item {
    padding: var(--global-spacing-2) var(--global-spacing-3);
    font-size: var(--global-font-size-sm);
  }

  .profile {
    img {
      width: 32px;
      height: 32px;
    }

    .profile-info {
      strong {
        font-size: var(--global-font-size-sm);
      }
      span {
        font-size: var(--global-font-size-xs);
      }
    }
  }
}

import { Routes } from '@angular/router';
import { TestButtonComponent } from './test-components/test-button/test-button.component';
import { TestTextboxComponent } from './test-components/test-textbox/test-textbox.component';
import { TestCheckboxComponent } from './test-components/test-checkbox/test-checkbox.component';
import { TestRadioButtonComponent } from './test-components/test-radio-button/test-radio-button.component';
import { TestAvatarsComponent } from './test-components/test-avatars/test-avatars.component';
import { TestToggleComponent } from './test-components/test-toggle/test-toggle.component';
import { TestBadgesComponent } from './test-components/test-badges/test-badges.component';
import { TestSliderComponent } from './test-components/test-slider/test-slider.component';
import { TestSearchBarComponent } from './test-components/test-search-bar/test-search-bar.component';
import { TestStatusBadgeComponent } from './test-components/test-status-badge/test-status-badge.component';
import { TestDividersComponent } from './test-components/test-dividers/test-dividers.component';
import { TestPaginationComponent } from './test-components/test-pagination/test-pagination.component';
import { TestProgressbarComponent } from './test-components/test-progressbar/test-progressbar.component';
import { TestTooltipComponent } from './test-components/test-tooltip/test-tooltip.component';
import { TestSnackbarComponent } from './test-components/test-snackbar/test-snackbar.component';
import { TestStepperComponent } from './test-components/test-stepper/test-stepper.component';
import { TestToasterComponent } from './test-components/test-toaster/test-toaster.component';
import { TestRatingComponent } from './test-components/test-rating/test-rating.component';
import { TestOtpComponent } from './test-components/test-otp/test-otp.component';
import { TestSpinnerComponent } from './test-components/test-spinner/test-spinner.component';
import { TestTagsComponent } from './test-components/test-tags/test-tags.component';
import { TestSelectComponent } from './test-components/test-select/test-select.component';
import { TestSSOLoginComponent } from './test-components/test-sso-login/test-sso-login.component';
import { TestTabsComponent } from './test-components/test-tabs/test-tabs.component';
import { TestModalComponent } from './test-components/test-modal/test-modal.component';
import { TestAccordionComponent } from './test-components/test-accordion/test-accordion.component';
import { TestDialogComponent } from './test-components/test-dialog/test-dialog.component';
import { TestListCardComponent } from './test-components/test-list-card/test-list-card.component';
import { TestImgCardComponent } from './test-components/test-img-card/test-img-card.component';
import { TestBasicCardComponent } from './test-components/test-basic-card/test-basic-card.component';
import { TestTreeviewComponent } from './test-components/test-treeview/test-treeview.component';
import { TestSidebarComponent } from './test-components/test-sidebar/test-sidebar.component';

export const troutes: Routes = [
  // Test component routes (alphabetical order)
  { path: 'test-accordion', component: TestAccordionComponent },
  { path: 'test-avatars', component: TestAvatarsComponent },
  { path: 'test-badge', component: TestBadgesComponent },
  { path: 'test-basic-card', component: TestBasicCardComponent },
  { path: 'test-button', component: TestButtonComponent },
  { path: 'test-checkbox', component: TestCheckboxComponent },
  { path: 'test-dialog', component: TestDialogComponent },
  { path: 'test-dividers', component: TestDividersComponent },
  { path: 'test-img-card', component: TestImgCardComponent },
  { path: 'test-list-card', component: TestListCardComponent },
  { path: 'test-modal', component: TestModalComponent },
  { path: 'test-otp', component: TestOtpComponent },
  { path: 'test-pagination', component: TestPaginationComponent },
  { path: 'test-progressbar', component: TestProgressbarComponent },
  { path: 'test-radio-button', component: TestRadioButtonComponent },
  { path: 'test-rating', component: TestRatingComponent },
  { path: 'test-search-bar', component: TestSearchBarComponent },
  { path: 'test-select', component: TestSelectComponent },
  { path: 'test-slider', component: TestSliderComponent },
  { path: 'test-snackbar', component: TestSnackbarComponent },
  { path: 'test-spinner', component: TestSpinnerComponent },
  { path: 'test-sso-login', component: TestSSOLoginComponent },
  { path: 'test-status-badge', component: TestStatusBadgeComponent },
  { path: 'test-stepper', component: TestStepperComponent },
  { path: 'test-tabs', component: TestTabsComponent },
  { path: 'test-tags', component: TestTagsComponent },
  { path: 'test-textbox', component: TestTextboxComponent },
  { path: 'test-toggle', component: TestToggleComponent },
  { path: 'test-toaster', component: TestToasterComponent },
  { path: 'test-tooltip', component: TestTooltipComponent },
  { path: 'test-treeview', component: TestTreeviewComponent },
  {path:'test-sidebar', component: TestSidebarComponent},
];


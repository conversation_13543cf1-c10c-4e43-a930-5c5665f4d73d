import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

interface NavigationItem {
  id: string;
  label: string;
  iconName: string;
  active?: boolean;
  badge?: string;
}

@Component({
  selector: 'app-test-sidebar',
  standalone: true,
  imports: [CommonModule, SidebarComponent, IconComponent, ButtonComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrls: ['./test-sidebar.component.scss']
})
export class TestSidebarComponent {
  // Sidebar state management
  smallSidebarCollapsed = false;
  mediumSidebarCollapsed = false;
  largeSidebarCollapsed = false;

  // Navigation items for the sidebar
  navigationItems: NavigationItem[] = [
    { id: 'dashboard', label: 'Dashboard', iconName: 'layout-dashboard', active: true },
    { id: 'analytics', label: 'Analytics', iconName: 'trending-up', badge: '3' },
    { id: 'projects', label: 'Projects', iconName: 'folder' },
    { id: 'tasks', label: 'Tasks', iconName: 'check-square', badge: '12' },
    { id: 'team', label: 'Team', iconName: 'users' },
    { id: 'calendar', label: 'Calendar', iconName: 'calendar' },
    { id: 'documents', label: 'Documents', iconName: 'file-text' },
    { id: 'reports', label: 'Reports', iconName: 'bar-chart-3' }
  ];

  // Search functionality
  searchQuery = '';

  // User profile data
  userProfile = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'user',
    status: 'online'
  };

  // Demo sections for testing different sidebar configurations
  demoSections = [
    {
      title: 'Small Sidebar',
      description: 'Compact sidebar perfect for minimal interfaces',
      size: 'small' as const,
      collapsed: false
    },
    {
      title: 'Medium Sidebar',
      description: 'Standard sidebar with balanced content and navigation',
      size: 'medium' as const,
      collapsed: false
    },
    {
      title: 'Large Sidebar',
      description: 'Spacious sidebar with detailed navigation and content',
      size: 'large' as const,
      collapsed: false
    }
  ];

  // Handle navigation item clicks
  onNavigationItemClick(item: NavigationItem): void {
    // Reset all items to inactive
    this.navigationItems.forEach(navItem => navItem.active = false);
    // Set clicked item as active
    item.active = true;
    console.log('Navigation item clicked:', item);
  }

  // Handle search input
  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchQuery = target.value;
    console.log('Search query:', this.searchQuery);
  }

  // Handle search submit
  onSearchSubmit(): void {
    if (this.searchQuery.trim()) {
      console.log('Search submitted:', this.searchQuery);
    }
  }

  // Handle user profile actions
  onProfileAction(action: string): void {
    console.log('Profile action:', action);
  }

  // Toggle sidebar collapse state
  toggleSidebar(size: 'small' | 'medium' | 'large'): void {
    switch (size) {
      case 'small':
        this.smallSidebarCollapsed = !this.smallSidebarCollapsed;
        break;
      case 'medium':
        this.mediumSidebarCollapsed = !this.mediumSidebarCollapsed;
        break;
      case 'large':
        this.largeSidebarCollapsed = !this.largeSidebarCollapsed;
        break;
    }
  }

  // Get collapsed state for a specific size
  getCollapsedState(size: 'small' | 'medium' | 'large'): boolean {
    switch (size) {
      case 'small':
        return this.smallSidebarCollapsed;
      case 'medium':
        return this.mediumSidebarCollapsed;
      case 'large':
        return this.largeSidebarCollapsed;
      default:
        return false;
    }
  }

  // Handle sidebar collapse change
  onSidebarCollapseChange(collapsed: boolean, size: 'small' | 'medium' | 'large'): void {
    switch (size) {
      case 'small':
        this.smallSidebarCollapsed = collapsed;
        break;
      case 'medium':
        this.mediumSidebarCollapsed = collapsed;
        break;
      case 'large':
        this.largeSidebarCollapsed = collapsed;
        break;
    }
  }
}

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchBarComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/search-bar/search-bar.component';

@Component({
  selector: 'ava-search-bar-icons-demo',
  standalone: true,
  imports: [CommonModule, SearchBarComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Search Bar Icons</h3>
        <p>
          Different icon configurations and customizations for the search bar
          component.
        </p>
      </div>

      <div class="icons-demo">
        <div class="demo-section">
          <h4>Default Icons</h4>
          <p class="icon-description">
            Standard search and send icons with default colors
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Default Icons"
              placeholder="Search with default icons..."
              (searchClick)="onSearch($event, 'default')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Custom Icon Colors</h4>
          <p class="icon-description">
            Different colors for search and send icons
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Custom Colors"
              placeholder="Search with custom icon colors..."
              searchIconColor="#6366f1"
              sendIconColor="#8b5cf6"
              (searchClick)="onSearch($event, 'custom-colors')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Matching Icon Colors</h4>
          <p class="icon-description">
            Both icons using the same color for consistency
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Matching Colors"
              placeholder="Search with matching icon colors..."
              searchIconColor="#059669"
              sendIconColor="#059669"
              (searchClick)="onSearch($event, 'matching')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Icon Color Combinations</h4>
          <p class="icon-description">
            Various color combinations for different use cases
          </p>
          <div class="icon-combinations">
            <div class="icon-item">
              <label for="blue-search">Blue Theme</label>
              <ava-search-bar
                id="blue-search"
                placeholder="Blue theme"
                searchIconColor="#3b82f6"
                sendIconColor="#1d4ed8"
                (searchClick)="onSearch($event, 'blue')"
              ></ava-search-bar>
            </div>
            <div class="icon-item">
              <label for="green-search">Green Theme</label>
              <ava-search-bar
                id="green-search"
                placeholder="Green theme"
                searchIconColor="#10b981"
                sendIconColor="#047857"
                (searchClick)="onSearch($event, 'green')"
              ></ava-search-bar>
            </div>
            <div class="icon-item">
              <label for="purple-search">Purple Theme</label>
              <ava-search-bar
                id="purple-search"
                placeholder="Purple theme"
                searchIconColor="#8b5cf6"
                sendIconColor="#7c3aed"
                (searchClick)="onSearch($event, 'purple')"
              ></ava-search-bar>
            </div>
            <div class="icon-item">
              <label for="orange-search">Orange Theme</label>
              <ava-search-bar
                id="orange-search"
                placeholder="Orange theme"
                searchIconColor="#f97316"
                sendIconColor="#ea580c"
                (searchClick)="onSearch($event, 'orange')"
              ></ava-search-bar>
            </div>
          </div>
        </div>

        <div class="demo-section">
          <h4>Accessibility Focus</h4>
          <p class="icon-description">
            High contrast colors for better accessibility
          </p>
          <div class="search-example">
            <ava-search-bar
              label="High Contrast"
              placeholder="Search with high contrast icons..."
              searchIconColor="#000000"
              sendIconColor="#ffffff"
              (searchClick)="onSearch($event, 'high-contrast')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Icon Size Variations</h4>
          <p class="icon-description">
            Icons scale with component size for proper proportions
          </p>
          <div class="size-variations">
            <div class="size-item">
              <label for="xs-search">Extra Small (16px icons)</label>
              <ava-search-bar
                id="xs-search"
                placeholder="XS"
                size="xs"
                searchIconColor="#6b7280"
                sendIconColor="#6b7280"
                (searchClick)="onSearch($event, 'xs')"
              ></ava-search-bar>
            </div>
            <div class="size-item">
              <label for="md-search">Medium (20px icons)</label>
              <ava-search-bar
                id="md-search"
                placeholder="MD"
                size="md"
                searchIconColor="#6b7280"
                sendIconColor="#6b7280"
                (searchClick)="onSearch($event, 'md')"
              ></ava-search-bar>
            </div>
            <div class="size-item">
              <label for="lg-search">Large (24px icons)</label>
              <ava-search-bar
                id="lg-search"
                placeholder="LG"
                size="lg"
                searchIconColor="#6b7280"
                sendIconColor="#6b7280"
                (searchClick)="onSearch($event, 'lg')"
              ></ava-search-bar>
            </div>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h4>Icon Features</h4>
        <ul class="feature-list">
          <li>
            <strong>Dynamic Sizing:</strong> Icon sizes are computed based on
            component size
          </li>
          <li>
            <strong>Color Management:</strong> Handles disabled states with CSS
            variable fallbacks
          </li>
          <li>
            <strong>Interactive Elements:</strong> Send icon is clickable with
            proper cursor styling
          </li>
          <li>
            <strong>Accessibility:</strong> High contrast options for better
            visibility
          </li>
        </ul>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .icons-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .icon-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 1.5rem;
        font-style: italic;
      }

      .search-example {
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
      }

      .icon-combinations {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }

      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .icon-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .size-variations {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }

      .size-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .size-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .info-section {
        padding: 1.5rem;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;
      }

      .info-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 18px;
        font-weight: 500;
      }

      .feature-list {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
        line-height: 1.6;
      }

      .feature-list li {
        margin-bottom: 0.5rem;
      }

      .feature-list strong {
        color: #333;
      }
    `,
  ],
})
export class IconsDemoComponent {
  onSearch(searchTerm: string, variant: string) {
    console.log(`Search clicked with variant ${variant}:`, searchTerm);
  }

  onTextboxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    console.log('Textbox changed:', target.value);
  }
}

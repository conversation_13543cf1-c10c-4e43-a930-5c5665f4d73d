import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchBarComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/search-bar/search-bar.component';

@Component({
  selector: 'ava-search-bar-sizes-demo',
  standalone: true,
  imports: [CommonModule, SearchBarComponent],
  template: `
    <div class="demo-container">
      <div class="sizes-demo">
        <div class="demo-section">
          <div class="size-comparison">
            <div class="size-item">
              <ava-search-bar
                placeholder="XS"
                size="xs"
                (searchClick)="onSearch($event, 'xs')"
              ></ava-search-bar>
            </div>
            <div class="size-item">
              <ava-search-bar
                placeholder="MD"
                size="md"
                (searchClick)="onSearch($event, 'md')"
              ></ava-search-bar>
            </div>
            <div class="size-item">
              <ava-search-bar
                placeholder="LG"
                size="lg"
                (searchClick)="onSearch($event, 'lg')"
              ></ava-search-bar>
            </div>
            <div class="size-item">
              <ava-search-bar
                placeholder="XL"
                size="xl"
                (searchClick)="onSearch($event, 'xl')"
              ></ava-search-bar>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .sizes-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .size-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 1.5rem;
        font-style: italic;
      }

      .search-example {
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
      }

      .size-comparison {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
      }

      .size-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .size-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .info-section {
        padding: 1.5rem;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;
      }

      .info-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 18px;
        font-weight: 500;
      }

      .feature-list {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
        line-height: 1.6;
      }

      .feature-list li {
        margin-bottom: 0.5rem;
      }

      .feature-list strong {
        color: #333;
      }
    `,
  ],
})
export class SizesDemoComponent {
  onSearch(searchTerm: string, size: string) {
    console.log(`Search clicked with size ${size}:`, searchTerm);
  }

  onTextboxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    console.log('Textbox changed:', target.value);
  }
}

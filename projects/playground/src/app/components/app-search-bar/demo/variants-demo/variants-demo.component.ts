import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchBarComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/search-bar/search-bar.component';

@Component({
  selector: 'ava-search-bar-variants-demo',
  standalone: true,
  imports: [CommonModule, SearchBarComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Search Bar Variants</h3>
        <p>
          Different visual variants for various design requirements and use
          cases.
        </p>
      </div>

      <div class="variants-demo">
        <div class="demo-section">
          <h4>Default Variant</h4>
          <p class="variant-description">
            Standard search bar with default styling
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Default Search"
              placeholder="Search with default styling..."
              (searchClick)="onSearch($event, 'default')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Custom Icon Colors</h4>
          <p class="variant-description">
            Search bar with custom search and send icon colors
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Custom Icons"
              placeholder="Search with custom colors..."
              searchIconColor="#8b5cf6"
              sendIconColor="#ec4899"
              (searchClick)="onSearch($event, 'custom-icons')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Brand Colors</h4>
          <p class="variant-description">
            Search bar using brand-specific colors
          </p>
          <div class="search-example">
            <ava-search-bar
              label="Brand Search"
              placeholder="Search with brand colors..."
              searchIconColor="#3b82f6"
              sendIconColor="#10b981"
              (searchClick)="onSearch($event, 'brand')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>High Contrast</h4>
          <p class="variant-description">
            Search bar with high contrast colors for accessibility
          </p>
          <div class="search-example">
            <ava-search-bar
              label="High Contrast"
              placeholder="Search with high contrast..."
              searchIconColor="#1f2937"
              sendIconColor="#dc2626"
              (searchClick)="onSearch($event, 'high-contrast')"
              (textboxChange)="onTextboxChange($event)"
            ></ava-search-bar>
          </div>
        </div>

        <div class="demo-section">
          <h4>Theme Variants</h4>
          <p class="variant-description">Different theme-based color schemes</p>
          <div class="theme-variants">
            <div class="theme-item">
              <label for="primary-search">Primary Theme</label>
              <ava-search-bar
                id="primary-search"
                placeholder="Primary"
                searchIconColor="#3b82f6"
                sendIconColor="#3b82f6"
                (searchClick)="onSearch($event, 'primary')"
              ></ava-search-bar>
            </div>
            <div class="theme-item">
              <label for="success-search">Success Theme</label>
              <ava-search-bar
                id="success-search"
                placeholder="Success"
                searchIconColor="#10b981"
                sendIconColor="#10b981"
                (searchClick)="onSearch($event, 'success')"
              ></ava-search-bar>
            </div>
            <div class="theme-item">
              <label for="warning-search">Warning Theme</label>
              <ava-search-bar
                id="warning-search"
                placeholder="Warning"
                searchIconColor="#f59e0b"
                sendIconColor="#f59e0b"
                (searchClick)="onSearch($event, 'warning')"
              ></ava-search-bar>
            </div>
            <div class="theme-item">
              <label for="danger-search">Danger Theme</label>
              <ava-search-bar
                id="danger-search"
                placeholder="Danger"
                searchIconColor="#ef4444"
                sendIconColor="#ef4444"
                (searchClick)="onSearch($event, 'danger')"
              ></ava-search-bar>
            </div>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h4>Variant Features</h4>
        <ul class="feature-list">
          <li>
            <strong>Custom Icon Colors:</strong> Flexible color customization
            for search and send icons
          </li>
          <li>
            <strong>Brand Integration:</strong> Use brand colors for consistent
            visual identity
          </li>
          <li>
            <strong>Accessibility:</strong> High contrast options for better
            visibility
          </li>
          <li>
            <strong>Theme Support:</strong> Multiple theme-based color schemes
          </li>
        </ul>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .variants-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .variant-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 1.5rem;
        font-style: italic;
      }

      .search-example {
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
      }

      .theme-variants {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }

      .theme-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .theme-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .info-section {
        padding: 1.5rem;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;
      }

      .info-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 18px;
        font-weight: 500;
      }

      .feature-list {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
        line-height: 1.6;
      }

      .feature-list li {
        margin-bottom: 0.5rem;
      }

      .feature-list strong {
        color: #333;
      }
    `,
  ],
})
export class VariantsDemoComponent {
  onSearch(searchTerm: string, variant: string) {
    console.log(`Search clicked with variant ${variant}:`, searchTerm);
  }

  onTextboxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    console.log('Textbox changed:', target.value);
  }
}

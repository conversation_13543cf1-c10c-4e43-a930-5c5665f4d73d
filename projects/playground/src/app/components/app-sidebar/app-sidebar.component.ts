import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

interface DocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

interface ApiEvent {
  name: string;
  type: string;
  description: string;
}

@Component({
  selector: 'ava-sidebar-demo',
  standalone: true,
  imports: [CommonModule, SidebarComponent, ButtonComponent, IconComponent],
  templateUrl: './app-sidebar.component.html',
  styleUrl: './app-sidebar.component.scss',
})
export class AppSidebarComponent {
  // State for different sidebar variants
  smallSidebarCollapsed = false;
  mediumSidebarCollapsed = false;
  largeSidebarCollapsed = false;
  leftSidebarCollapsed = false;
  rightSidebarCollapsed = false;

  // Documentation sections
  docSections: DocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple sidebar with header, content, and footer sections.',
      showCode: false,
    },
    {
      title: 'Size Variants',
      description: 'Different size options: small, medium, and large.',
      showCode: false,
    },
    {
      title: 'Position Variants',
      description: 'Sidebar can be positioned on the left or right side.',
      showCode: false,
    },
    {
      title: 'Collapsible Sidebar',
      description: 'Sidebar with collapse/expand functionality.',
      showCode: false,
    },
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'size',
      type: "'small' | 'medium' | 'large'",
      default: "'medium'",
      description: 'Sets the width of the sidebar when expanded.',
    },
    {
      name: 'position',
      type: "'left' | 'right'",
      default: "'left'",
      description: 'Determines the position of the sidebar on the screen.',
    },
    {
      name: 'collapsible',
      type: 'boolean',
      default: 'true',
      description: 'Whether the sidebar can be collapsed.',
    },
    {
      name: 'collapsed',
      type: 'boolean',
      default: 'false',
      description: 'Controls the collapsed state of the sidebar. Supports two-way binding.',
    },
  ];

  apiEvents: ApiEvent[] = [
    {
      name: 'collapsedChange',
      type: 'EventEmitter<boolean>',
      description: 'Emitted when the collapsed state changes. Enables two-way binding with [(collapsed)].',
    },
  ];

  // Scroll to demo section
  scrollToDemo(): void {
    const demoSection = document.querySelector('.demo-sections');
    if (demoSection) {
      demoSection.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Toggle code visibility
  toggleCode(section: DocSection): void {
    section.showCode = !section.showCode;
  }

  // Copy code to clipboard
  copyCode(sectionTitle: string): void {
    const code = this.getExampleCode(sectionTitle);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    });
  }

  // Get example code for each section
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-sidebar',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
    <ava-sidebar>
      <div sidebar-header>
        <img src="logo.svg" alt="Logo">
      </div>

      <div sidebar-content>
        <nav>
          <div class="nav-item">Dashboard</div>
          <div class="nav-item">Settings</div>
        </nav>
      </div>

      <div sidebar-footer>
        <div class="user-profile">John Doe</div>
      </div>
    </ava-sidebar>
  \`
})
export class BasicSidebarComponent {}`,

      'size variants': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-size-variants',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
    <!-- Small sidebar -->
    <ava-sidebar size="small">
      <div sidebar-content>Small sidebar content</div>
    </ava-sidebar>

    <!-- Medium sidebar (default) -->
    <ava-sidebar size="medium">
      <div sidebar-content>Medium sidebar content</div>
    </ava-sidebar>

    <!-- Large sidebar -->
    <ava-sidebar size="large">
      <div sidebar-content>Large sidebar content</div>
    </ava-sidebar>
  \`
})
export class SizeVariantsComponent {}`,

      'position variants': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-position-variants',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
    <!-- Left positioned sidebar (default) -->
    <ava-sidebar position="left">
      <div sidebar-content>Left sidebar</div>
    </ava-sidebar>

    <!-- Right positioned sidebar -->
    <ava-sidebar position="right">
      <div sidebar-content>Right sidebar</div>
    </ava-sidebar>
  \`
})
export class PositionVariantsComponent {}`,

      'collapsible sidebar': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-collapsible-sidebar',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
    <ava-sidebar
      [(collapsed)]="isCollapsed"
      (collapsedChange)="onCollapseChange($event)">

      <div sidebar-content>
        <div class="nav-item">
          <span class="sidebar-item-text">Dashboard</span>
        </div>
      </div>
    </ava-sidebar>

    <button (click)="toggleSidebar()">
      {{ isCollapsed ? 'Expand' : 'Collapse' }} Sidebar
    </button>
  \`
})
export class CollapsibleSidebarComponent {
  isCollapsed = false;

  toggleSidebar() {
    this.isCollapsed = !this.isCollapsed;
  }

  onCollapseChange(collapsed: boolean) {
    console.log('Sidebar collapsed:', collapsed);
  }
}`,
    };

    return examples[section.toLowerCase()] || '';
  }
}

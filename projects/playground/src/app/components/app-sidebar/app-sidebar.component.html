<div class="sidebar-demo">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <h1>Sidebar Component</h1>
      <p>A flexible and responsive sidebar component with collapsible functionality, multiple size variants, and customizable positioning.</p>
      <div class="scroll-indicator" (click)="scrollToDemo()">
        <span>Explore Demos</span>
        <ava-icon iconName="chevron-down" [iconSize]="24" iconColor="white"></ava-icon>
      </div>
    </div>
  </section>

  <!-- Demo Sections -->
  <div class="demo-sections">
    <!-- Basic Usage -->
    <section class="layout-section" *ngFor="let section of docSections; let i = index">
      <div class="section-header">
        <h2>{{ section.title }}</h2>
        <p>{{ section.description }}</p>
      </div>

      <div class="full-layout">
        <div class="demo-header-bar">
          <div class="header-left">
            <h3>{{ section.title }} Demo</h3>
          </div>
          <div class="header-right">
            <ava-button
              label="View Code"
              variant="secondary"
              size="small"
              (userClick)="toggleCode(section)">
            </ava-button>
            <ava-button
              label="Copy Code"
              variant="primary"
              size="small"
              (userClick)="copyCode(section.title)">
            </ava-button>
          </div>
        </div>

        <div class="layout-content">
          <!-- Demo Content -->
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage Demo -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <ava-sidebar [(collapsed)]="smallSidebarCollapsed" size="medium" position="left">
                <div sidebar-header class="demo-header-content">
                  <ava-icon iconName="layers" [iconSize]="24" iconColor="#667eea"></ava-icon>
                  <span class="header-title sidebar-item-text">Dashboard</span>
                </div>

                <div sidebar-content class="demo-content">
                  <div class="nav-item active">
                    <ava-icon iconName="home" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Home</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="settings" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Settings</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="users" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Users</span>
                  </div>
                </div>

                <div sidebar-footer class="demo-footer-content">
                  <div class="user-info">
                    <ava-icon iconName="user" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">John Doe</span>
                  </div>
                </div>
              </ava-sidebar>
            </ng-container>

            <!-- Size Variants Demo -->
            <ng-container *ngSwitchCase="'Size Variants'">
              <ava-sidebar [(collapsed)]="mediumSidebarCollapsed" size="small" position="left">
                <div sidebar-header class="demo-header-content">
                  <ava-icon iconName="minimize-2" [iconSize]="20" iconColor="#667eea"></ava-icon>
                  <span class="header-title sidebar-item-text">Small</span>
                </div>
                <div sidebar-content class="demo-content">
                  <div class="nav-item active">
                    <ava-icon iconName="home" [iconSize]="18"></ava-icon>
                    <span class="sidebar-item-text">Home</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="settings" [iconSize]="18"></ava-icon>
                    <span class="sidebar-item-text">Settings</span>
                  </div>
                </div>
              </ava-sidebar>
            </ng-container>

            <!-- Position Variants Demo -->
            <ng-container *ngSwitchCase="'Position Variants'">
              <ava-sidebar [(collapsed)]="rightSidebarCollapsed" size="medium" position="right">
                <div sidebar-header class="demo-header-content">
                  <ava-icon iconName="arrow-right" [iconSize]="24" iconColor="#667eea"></ava-icon>
                  <span class="header-title sidebar-item-text">Right Sidebar</span>
                </div>
                <div sidebar-content class="demo-content">
                  <div class="nav-item active">
                    <ava-icon iconName="home" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Home</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="settings" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Settings</span>
                  </div>
                </div>
              </ava-sidebar>
            </ng-container>

            <!-- Collapsible Sidebar Demo -->
            <ng-container *ngSwitchCase="'Collapsible Sidebar'">
              <ava-sidebar [(collapsed)]="largeSidebarCollapsed" size="large" position="left">
                <div sidebar-header class="demo-header-content">
                  <ava-icon iconName="menu" [iconSize]="24" iconColor="#667eea"></ava-icon>
                  <span class="header-title sidebar-item-text">Collapsible</span>
                </div>
                <div sidebar-content class="demo-content">
                  <div class="nav-item active">
                    <ava-icon iconName="layout-dashboard" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Dashboard</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="trending-up" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Analytics</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="users" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Users</span>
                  </div>
                  <div class="nav-item">
                    <ava-icon iconName="settings" [iconSize]="20"></ava-icon>
                    <span class="sidebar-item-text">Settings</span>
                  </div>
                </div>
                <div sidebar-footer class="demo-footer-content">
                  <div class="storage-info">
                    <small class="sidebar-item-text">Storage: 75% used</small>
                    <div class="storage-bar sidebar-item-text">
                      <div class="storage-used" style="width: 75%"></div>
                    </div>
                  </div>
                </div>
              </ava-sidebar>
            </ng-container>
          </ng-container>

          <!-- Main Content Area -->
          <div class="main-content">
            <div class="content-header">
              <h2>{{ section.title }} Example</h2>
              <p>This demonstrates the {{ section.title.toLowerCase() }} functionality of the sidebar component.</p>
            </div>

            <div class="content-body">
              <div class="content-card">
                <h3>Interactive Demo</h3>
                <p>Try collapsing and expanding the sidebar using the toggle button. Notice how the content adapts to the sidebar state.</p>

                <div class="demo-controls" *ngIf="section.title === 'Collapsible Sidebar'">
                  <ava-button
                    [label]="largeSidebarCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'"
                    variant="primary"
                    (userClick)="largeSidebarCollapsed = !largeSidebarCollapsed">
                  </ava-button>
                </div>

                <div class="stats-grid" *ngIf="section.title === 'Basic Usage'">
                  <div class="stat-item">
                    <ava-icon iconName="users" [iconSize]="24"></ava-icon>
                    <div class="stat-info">
                      <div class="stat-number">1,234</div>
                      <div class="stat-label">Total Users</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <ava-icon iconName="activity" [iconSize]="24"></ava-icon>
                    <div class="stat-info">
                      <div class="stat-number">89%</div>
                      <div class="stat-label">Uptime</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Code Block -->
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title)"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title)">
            <ava-icon iconName="copy" [iconSize]="16" iconColor="white"></ava-icon>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Documentation Section -->
  <section class="api-section">
    <div class="section-header">
      <h2>API Reference</h2>
      <p>Complete documentation of all properties and events for the sidebar component.</p>
    </div>

    <div class="api-content">
      <div class="api-table">
        <h3>Properties</h3>
        <table>
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="api-table">
        <h3>Events</h3>
        <table>
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let event of apiEvents">
              <td><code>{{ event.name }}</code></td>
              <td><code>{{ event.type }}</code></td>
              <td>{{ event.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
</div>

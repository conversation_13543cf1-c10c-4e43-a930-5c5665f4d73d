import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { SelectComponent } from '../../../../../../../play-comp-library/src/lib/components/select/select.component';
import { SelectOptionComponent } from '../../../../../../../play-comp-library/src/lib/components/select/select-option/select-option.component';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-skeleton-loading-demo',
  imports: [
    CommonModule,
    FormsModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    ButtonComponent,
    SelectComponent,
    SelectOptionComponent,
  ],
  templateUrl: './skeleton-loading-demo.component.html',
  styleUrl: './skeleton-loading-demo.component.scss',
})
export class SkeletonLoadingDemoComponent {
  employeeData = [
    {
      id: 1,
      name: 'Alice Johnson',
      position: 'Senior Developer',
      salary: 95000,
      joinDate: '2020-03-15',
      experience: 8,
      department: 'Engineering',
    },
    {
      id: 2,
      name: 'Bob Smith',
      position: 'Marketing Manager',
      salary: 75000,
      joinDate: '2019-07-22',
      experience: 6,
      department: 'Marketing',
    },
    {
      id: 3,
      name: 'Carlos Martinez',
      position: 'Sales Representative',
      salary: 55000,
      joinDate: '2021-11-08',
      experience: 3,
      department: 'Sales',
    },
    {
      id: 4,
      name: 'Diana Lee',
      position: 'UX Designer',
      salary: 70000,
      joinDate: '2020-09-12',
      experience: 5,
      department: 'Design',
    },
    {
      id: 5,
      name: 'Ethan Brown',
      position: 'Data Analyst',
      salary: 65000,
      joinDate: '2022-01-30',
      experience: 2,
      department: 'Analytics',
    },
    {
      id: 6,
      name: 'Fiona Green',
      position: 'Project Manager',
      salary: 85000,
      joinDate: '2018-05-10',
      experience: 9,
      department: 'Operations',
    },
  ];

  displayedColumns = ['name', 'position', 'salary', 'experience', 'joinDate'];
  
  // Loading state controls
  isLoading = false;
  loadingType: 'skeleton' | 'cubical' = 'skeleton';

  // Simulate sort action with loading
  onSort() {
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
    }, 2000);
  }

  // Simulate filter action with loading
  onFilter() {
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
    }, 1500);
  }

  // Change loading type
  onLoadingTypeChange(type: any) {
    this.loadingType = type as 'skeleton' | 'cubical';
  }
}

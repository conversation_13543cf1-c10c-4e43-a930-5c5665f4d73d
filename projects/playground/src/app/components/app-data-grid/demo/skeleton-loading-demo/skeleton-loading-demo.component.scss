.demo-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 3rem;

  h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 600;
  }

  p {
    color: #7f8c8d;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.demo-controls {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    label {
      font-weight: 500;
      color: #495057;
      min-width: 100px;
    }

    ava-select {
      min-width: 150px;
    }

    ava-button {
      margin-right: 0.5rem;
    }
  }
}

.demo-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  border: 1px solid #e9ecef;
  margin-bottom: 2rem;

  .card-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .demo-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .control-group {
      flex-direction: column;
      align-items: stretch;

      label {
        min-width: auto;
        margin-bottom: 0.5rem;
      }
    }
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

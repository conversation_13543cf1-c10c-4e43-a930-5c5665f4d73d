<div class="demo-content">
  <div class="demo-section">
    <h2>Skeleton Loading Demo</h2>
    <p>
      Demonstrates skeleton loading vs cubical loading when sorting or filtering data.
      Click the buttons below to trigger loading states.
    </p>

    <!-- Controls -->
    <div class="demo-controls">
      <div class="control-group">
        <label>Loading Type:</label>
        <ava-select
          [(ngModel)]="loadingType"
          (selectionChange)="onLoadingTypeChange($event)"
          placeholder="Select loading type">
          <ava-select-option value="skeleton">Skeleton</ava-select-option>
          <ava-select-option value="cubical">Cubical</ava-select-option>
        </ava-select>
      </div>

      <div class="control-group">
        <ava-button 
          label="Simulate Sort" 
          variant="primary" 
          (userClick)="onSort()"
          [disabled]="isLoading">
        </ava-button>
        
        <ava-button 
          label="Simulate Filter" 
          variant="secondary" 
          (userClick)="onFilter()"
          [disabled]="isLoading">
        </ava-button>
      </div>
    </div>

    <!-- Data Grid -->
    <div class="demo-card">
      <div class="card-content">
        <ava-data-grid
          [dataSource]="employeeData"
          [displayedColumns]="displayedColumns"
          [isLoading]="isLoading"
          [loadingType]="loadingType"
        >
          <ng-container avaColumnDef="name" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Employee Name</ng-container>
            <ng-container *avaCellDef="let row">{{ row.name }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="position" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Position</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.position
            }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="salary" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Annual Salary</ng-container>
            <ng-container *avaCellDef="let row"
              >${{ row.salary | number }}</ng-container
            >
          </ng-container>

          <ng-container avaColumnDef="experience" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Experience (Years)</ng-container>
            <ng-container *avaCellDef="let row"
              >{{ row.experience }} years</ng-container
            >
          </ng-container>

          <ng-container avaColumnDef="joinDate" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Join Date</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.joinDate | date
            }}</ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
</div>

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomSidebarComponent } from '../../../../../play-comp-library/src/lib/composite-components/custom-sidebar/custom-sidebar.component';

@Component({
  selector: 'ava-custom-sidebar-demo',
  standalone: true,
  imports: [CommonModule, CustomSidebarComponent],
  templateUrl: './app-custom-sidebar.component.html',
  styleUrl: './app-custom-sidebar.component.scss',
})
export class AppCustomSidebarComponent {
  sidebarItems = [
    { id: '1', text: 'Dashboard', icon: 'dashboard', active: true },
    { id: '2', text: 'Projects', icon: 'folder', active: false },
    { id: '3', text: 'Tasks', icon: 'checklist', active: false },
    { id: '4', text: 'Messages', icon: 'message', active: false },
    { id: '5', text: 'Settings', icon: 'settings', active: false },
  ];

  onItemClick(item: {
    id: string;
    text: string;
    icon: string;
    active: boolean;
  }) {
    // Update active state
    this.sidebarItems.forEach((sidebarItem) => (sidebarItem.active = false));
    item.active = true;
    console.log('Sidebar item clicked:', item);
  }
}
